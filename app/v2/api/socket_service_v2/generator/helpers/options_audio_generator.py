"""
Options Audio Generator for Task Utils V2
Handles sequential generation of audio for task options.
"""

import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from bson.objectid import ObjectId

from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio

from .media_cache_helper import check_media_cache, save_media_cache
from .media_config_loader import MediaGenerationConfig
from .core_utils import serialize_usage_metadata

logger = setup_new_logging(__name__)


async def generate_options_audio(
    current_user: UserTenantDB,
    task_item: Dict[str, Any],
    task_id: ObjectId,
    socketio_server: Optional[Any] = None
):
    """
    Generate audio for all task options SEQUENTIALLY to avoid rate limits.

    This function processes options one by one with delays between each option
    to prevent API rate limiting issues.
    """
    try:
        # Load media configuration and check if options audio is enabled
        config_loader = MediaGenerationConfig(current_user)

        # Check if options audio is enabled globally
        if not config_loader.is_options_audio_enabled():
            logger.info(f"⏭️  TASK {task_id} | OPTIONS AUDIO: DISABLED GLOBALLY")
            return

        question = task_item.get("question", {})
        options = question.get("options", {})
        
        if not options:
            logger.debug(f"📝 Task {task_id} has no options, skipping options audio generation")
            return

        logger.info(f"🔊 SEQUENTIAL: Generating audio for {len(options)} options in task {task_id}")
        
        options_metadata = {}
        total_options = len(options)
        
        # Process each option sequentially with delays
        for i, (option_key, option_value) in enumerate(options.items()):
            try:
                option_number = i + 1
                logger.info(f"🎵 OPTION {option_number}/{total_options} | TASK {task_id} | KEY: {option_key} | VALUE: {option_value}")
                
                # Generate audio for this option
                option_audio_url = await _generate_single_option_audio(
                    current_user, str(option_value), task_id, option_key, option_number, total_options
                )
                
                if option_audio_url:
                    options_metadata[option_key] = {
                        "text": str(option_value),
                        "audio_url": option_audio_url,
                        "generated_at": datetime.now(timezone.utc).isoformat(),
                        "cached": False  # Will be updated if cached
                    }
                    logger.info(f"✅ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: SUCCESS")
                else:
                    logger.error(f"❌ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: FAILED")
                    options_metadata[option_key] = {
                        "text": str(option_value),
                        "audio_url": None,
                        "error": "Failed to generate audio",
                        "generated_at": datetime.now(timezone.utc).isoformat()
                    }
                
                # Add delay between options to avoid rate limits (except after last option)
                if i < total_options - 1:
                    delay_seconds = 2  # 2 second delay between options
                    logger.info(f"⏳ Waiting {delay_seconds}s before next option to avoid rate limits...")
                    await asyncio.sleep(delay_seconds)
                
            except Exception as option_error:
                logger.error(f"❌ Failed to generate audio for option {option_key}: {option_error}")
                options_metadata[option_key] = {
                    "text": str(option_value),
                    "audio_url": None,
                    "error": str(option_error),
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
                continue  # Continue with next option
        
        # Update task with all options metadata
        try:
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.options_metadata": options_metadata,
                        "metadata._options_audio_ready": True,
                        "metadata._options_processed": len(options_metadata),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            successful_options = sum(1 for meta in options_metadata.values() if meta.get("audio_url"))
            logger.info(f"✅ TASK {task_id} | OPTIONS AUDIO: {successful_options}/{total_options} successful")
            
        except Exception as db_error:
            logger.error(f"❌ Failed to update task {task_id} with options metadata: {db_error}")
            await _mark_options_audio_failed(current_user, task_id, str(db_error))

    except Exception as e:
        logger.error(f"❌ Failed to generate options audio for task {task_id}: {e}")
        await _mark_options_audio_failed(current_user, task_id, str(e))


async def _generate_single_option_audio(
    current_user: UserTenantDB,
    option_text: str,
    task_id: ObjectId,
    option_key: str,
    option_number: int,
    total_options: int
) -> Optional[str]:
    """Generate audio for a single option with caching."""
    try:
        # Check cache first
        cached_audio = await check_media_cache(current_user, option_text, "audio", "audio_prompt")
        
        if cached_audio:
            file_info = cached_audio["file_info"]
            audio_url = file_info.get("url")
            logger.info(f"🎯 OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: CACHED | URL: {audio_url}")
            return audio_url
        
        # Generate new audio
        logger.info(f"🎵 OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: GENERATING | TEXT: {option_text}")
        _file_text, file_info, usage_metadata = await generate_audio(current_user, option_text, "audio_prompt")
        
        if file_info and file_info.get("url"):
            audio_url = file_info.get("url")
            
            # Save to cache for future use
            await save_media_cache(
                current_user, option_text, "audio", "audio_prompt",
                _file_text or "", file_info, usage_metadata
            )
            
            logger.info(f"✅ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: GENERATED | URL: {audio_url}")
            return audio_url
        else:
            logger.error(f"❌ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: NO_DATA")
            return None
            
    except Exception as e:
        logger.error(f"❌ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: ERROR | {e}")
        return None


async def _mark_options_audio_failed(current_user: UserTenantDB, task_id: ObjectId, error_msg: str):
    """Mark options audio generation as failed."""
    try:
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata._options_audio_ready": False,
                    "metadata._options_audio_error": error_msg,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        logger.error(f"❌ Failed to mark options audio as failed for task {task_id}: {error_msg}")
    except Exception as db_error:
        logger.error(f"❌ Failed to mark options audio as failed for task {task_id}: {db_error}")
